{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/store/StoreHero.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Store Hero Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { motion } from 'framer-motion';\nimport { ShoppingCart, Truck, Shield, Headphones, Star } from 'lucide-react';\n\ninterface StoreHeroProps {\n  cartItemsCount: number;\n  onCartClick: () => void;\n}\n\nconst StoreHero = ({ cartItemsCount, onCartClick }: StoreHeroProps) => {\n  const features = [\n    {\n      icon: <Truck className=\"h-5 w-5\" />,\n      text: \"मोफत डिलिव्हरी\",\n      english: \"Free Delivery\"\n    },\n    {\n      icon: <Shield className=\"h-5 w-5\" />,\n      text: \"गुणवत्ता हमी\",\n      english: \"Quality Guarantee\"\n    },\n    {\n      icon: <Headphones className=\"h-5 w-5\" />,\n      text: \"24/7 सपोर्ट\",\n      english: \"24/7 Support\"\n    },\n    {\n      icon: <Star className=\"h-5 w-5\" />,\n      text: \"ब्रँडेड उत्पादने\",\n      english: \"Branded Products\"\n    }\n  ];\n\n  return (\n    <section className=\"relative bg-gradient-to-br from-orange-600 via-orange-500 to-yellow-500 py-16\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-1/4 left-1/4 w-32 h-32 border-2 border-white rounded-full spark-animation\"></div>\n        <div className=\"absolute top-3/4 right-1/4 w-24 h-24 border-2 border-white rounded-full spark-animation\" style={{animationDelay: '1s'}}></div>\n        <div className=\"absolute top-1/2 left-1/2 w-16 h-16 border-2 border-white rounded-full spark-animation\" style={{animationDelay: '2s'}}></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-start mb-8\">\n          {/* Title Section */}\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-white\"\n          >\n            <motion.h1\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n              className=\"text-4xl md:text-5xl font-bold marathi-text mb-2\"\n            >\n              ऑनलाइन स्टोअर\n            </motion.h1>\n            \n            <motion.h2\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4 }}\n              className=\"text-xl md:text-2xl font-semibold mb-4\"\n            >\n              Online Electrical Store\n            </motion.h2>\n\n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6 }}\n              className=\"text-lg text-orange-100 max-w-2xl marathi-text\"\n            >\n              घरी बसून इलेक्ट्रिकल उत्पादने ऑर्डर करा\n            </motion.p>\n          </motion.div>\n\n          {/* Cart Button */}\n          <motion.button\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ delay: 0.8, type: \"spring\", stiffness: 200 }}\n            onClick={onCartClick}\n            className=\"relative bg-white/20 backdrop-blur-sm text-white p-4 rounded-full hover:bg-white/30 transition-all duration-300 transform hover:scale-110\"\n          >\n            <ShoppingCart className=\"h-6 w-6\" />\n            {cartItemsCount > 0 && (\n              <span className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold\">\n                {cartItemsCount}\n              </span>\n            )}\n          </motion.button>\n        </div>\n\n        {/* Features */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1.0 }}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-4\"\n        >\n          {features.map((feature, index) => (\n            <motion.div\n              key={index}\n              initial={{ scale: 0 }}\n              animate={{ scale: 1 }}\n              transition={{ delay: 1.2 + index * 0.1, type: \"spring\", stiffness: 200 }}\n              className=\"bg-white/20 backdrop-blur-sm rounded-xl p-4 text-center text-white\"\n            >\n              <div className=\"flex justify-center mb-2\">\n                {feature.icon}\n              </div>\n              <div className=\"text-sm marathi-text font-semibold\">\n                {feature.text}\n              </div>\n              <div className=\"text-xs text-orange-100\">\n                {feature.english}\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Search Bar */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1.4 }}\n          className=\"mt-8 max-w-2xl mx-auto\"\n        >\n          <div className=\"relative\">\n            <input\n              type=\"text\"\n              placeholder=\"उत्पादन शोधा... / Search products...\"\n              className=\"w-full px-6 py-4 rounded-full text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-white/30\"\n            />\n            <button className=\"absolute right-2 top-2 bg-orange-600 text-white p-2 rounded-full hover:bg-orange-700 transition-colors duration-200\">\n              <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </button>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default StoreHero;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAUA,MAAM,YAAY;QAAC,EAAE,cAAc,EAAE,WAAW,EAAkB;IAChE,MAAM,WAAW;QACf;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,MAAM;YACN,SAAS;QACX;QACA;YACE,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,MAAM;YACN,SAAS;QACX;QACA;YACE,oBAAM,6LAAC,iNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,MAAM;YACN,SAAS;QACX;QACA;YACE,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,MAAM;YACN,SAAS;QACX;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAA0F,OAAO;4BAAC,gBAAgB;wBAAI;;;;;;kCACrI,6LAAC;wBAAI,WAAU;wBAAyF,OAAO;4BAAC,gBAAgB;wBAAI;;;;;;;;;;;;0BAGtI,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;kDACX;;;;;;kDAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;kDACX;;;;;;kDAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;kDACX;;;;;;;;;;;;0CAMH,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAE;gCACpB,YAAY;oCAAE,OAAO;oCAAK,MAAM;oCAAU,WAAW;gCAAI;gCACzD,SAAS;gCACT,WAAU;;kDAEV,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCACvB,iBAAiB,mBAChB,6LAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;;;;;;;kCAOT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAE;gCACpB,YAAY;oCAAE,OAAO,MAAM,QAAQ;oCAAK,MAAM;oCAAU,WAAW;gCAAI;gCACvE,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,IAAI;;;;;;kDAEf,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,IAAI;;;;;;kDAEf,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,OAAO;;;;;;;+BAbb;;;;;;;;;;kCAoBX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF;KA1IM;uCA4IS", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/store/ProductCategories.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Product Categories Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { motion } from 'framer-motion';\nimport { \n  Zap, \n  Lightbulb, \n  Fan, \n  Cable, \n  Settings, \n  Shield,\n  Grid3X3,\n  Wrench\n} from 'lucide-react';\n\ninterface ProductCategoriesProps {\n  selectedCategory: string;\n  onCategoryChange: (category: string) => void;\n}\n\nconst ProductCategories = ({ selectedCategory, onCategoryChange }: ProductCategoriesProps) => {\n  const categories = [\n    {\n      id: 'all',\n      icon: <Grid3X3 className=\"h-6 w-6\" />,\n      name: 'सर्व उत्पादने',\n      english: 'All Products',\n      count: 156,\n      color: 'bg-gray-600'\n    },\n    {\n      id: 'switches-sockets',\n      icon: <Zap className=\"h-6 w-6\" />,\n      name: 'स्विच आणि सॉकेट',\n      english: 'Switches & Sockets',\n      count: 45,\n      color: 'bg-blue-600'\n    },\n    {\n      id: 'lighting',\n      icon: <Lightbulb className=\"h-6 w-6\" />,\n      name: 'लाइटिंग',\n      english: 'Lighting',\n      count: 38,\n      color: 'bg-yellow-600'\n    },\n    {\n      id: 'fans',\n      icon: <Fan className=\"h-6 w-6\" />,\n      name: 'फॅन्स',\n      english: 'Fans',\n      count: 24,\n      color: 'bg-green-600'\n    },\n    {\n      id: 'wires-cables',\n      icon: <Cable className=\"h-6 w-6\" />,\n      name: 'वायर आणि केबल',\n      english: 'Wires & Cables',\n      count: 32,\n      color: 'bg-red-600'\n    },\n    {\n      id: 'accessories',\n      icon: <Settings className=\"h-6 w-6\" />,\n      name: 'अॅक्सेसरीज',\n      english: 'Accessories',\n      count: 17,\n      color: 'bg-purple-600'\n    },\n    {\n      id: 'safety',\n      icon: <Shield className=\"h-6 w-6\" />,\n      name: 'सेफ्टी उपकरणे',\n      english: 'Safety Equipment',\n      count: 12,\n      color: 'bg-orange-600'\n    },\n    {\n      id: 'tools',\n      icon: <Wrench className=\"h-6 w-6\" />,\n      name: 'टूल्स',\n      english: 'Tools',\n      count: 8,\n      color: 'bg-indigo-600'\n    }\n  ];\n\n  return (\n    <div className=\"mb-12\">\n      <motion.h3\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"text-2xl font-bold text-gray-900 mb-6 text-center marathi-text\"\n      >\n        उत्पादन श्रेणी निवडा\n      </motion.h3>\n\n      <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4\">\n        {categories.map((category, index) => (\n          <motion.button\n            key={category.id}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1 }}\n            onClick={() => onCategoryChange(category.id)}\n            className={`relative p-4 rounded-xl text-center transition-all duration-300 transform hover:scale-105 ${\n              selectedCategory === category.id\n                ? `${category.color} text-white shadow-lg`\n                : 'bg-white text-gray-700 hover:bg-gray-50 shadow-md'\n            }`}\n          >\n            {/* Icon */}\n            <div className={`flex justify-center mb-3 ${\n              selectedCategory === category.id ? 'text-white' : category.color.replace('bg-', 'text-')\n            }`}>\n              {category.icon}\n            </div>\n\n            {/* Category Name */}\n            <div className=\"space-y-1\">\n              <div className=\"text-sm font-semibold marathi-text\">\n                {category.name}\n              </div>\n              <div className={`text-xs ${\n                selectedCategory === category.id ? 'text-white/80' : 'text-gray-500'\n              }`}>\n                {category.english}\n              </div>\n            </div>\n\n            {/* Product Count */}\n            <div className={`absolute -top-2 -right-2 text-xs px-2 py-1 rounded-full font-bold ${\n              selectedCategory === category.id\n                ? 'bg-white/20 text-white'\n                : `${category.color} text-white`\n            }`}>\n              {category.count}\n            </div>\n\n            {/* Active Indicator */}\n            {selectedCategory === category.id && (\n              <motion.div\n                layoutId=\"activeCategory\"\n                className=\"absolute inset-0 border-2 border-white rounded-xl\"\n                initial={false}\n                transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n              />\n            )}\n          </motion.button>\n        ))}\n      </div>\n\n      {/* Category Description */}\n      <motion.div\n        key={selectedCategory}\n        initial={{ opacity: 0, y: 10 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"mt-6 text-center\"\n      >\n        <div className=\"inline-flex items-center space-x-2 bg-orange-100 text-orange-800 px-4 py-2 rounded-full\">\n          <span className=\"text-sm marathi-text\">\n            {selectedCategory === 'all' ? 'सर्व उत्पादने दाखवत आहे' : \n             `${categories.find(c => c.id === selectedCategory)?.name} श्रेणी निवडली`}\n          </span>\n          <span className=\"text-xs\">\n            ({categories.find(c => c.id === selectedCategory)?.count} उत्पादने)\n          </span>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default ProductCategories;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAmBA,MAAM,oBAAoB;QAAC,EAAE,gBAAgB,EAAE,gBAAgB,EAA0B;QA+IzE,kBAGF;IAjJZ,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,oBAAM,6LAAC,+MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,oBAAM,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,oBAAM,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gBACR,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;0BACX;;;;;;0BAID,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBAEZ,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAI;wBACjC,SAAS,IAAM,iBAAiB,SAAS,EAAE;wBAC3C,WAAW,AAAC,6FAIX,OAHC,qBAAqB,SAAS,EAAE,GAC5B,AAAC,GAAiB,OAAf,SAAS,KAAK,EAAC,2BAClB;;0CAIN,6LAAC;gCAAI,WAAW,AAAC,4BAEhB,OADC,qBAAqB,SAAS,EAAE,GAAG,eAAe,SAAS,KAAK,CAAC,OAAO,CAAC,OAAO;0CAE/E,SAAS,IAAI;;;;;;0CAIhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,SAAS,IAAI;;;;;;kDAEhB,6LAAC;wCAAI,WAAW,AAAC,WAEhB,OADC,qBAAqB,SAAS,EAAE,GAAG,kBAAkB;kDAEpD,SAAS,OAAO;;;;;;;;;;;;0CAKrB,6LAAC;gCAAI,WAAW,AAAC,qEAIhB,OAHC,qBAAqB,SAAS,EAAE,GAC5B,2BACA,AAAC,GAAiB,OAAf,SAAS,KAAK,EAAC;0CAErB,SAAS,KAAK;;;;;;4BAIhB,qBAAqB,SAAS,EAAE,kBAC/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAS;gCACT,WAAU;gCACV,SAAS;gCACT,YAAY;oCAAE,MAAM;oCAAU,WAAW;oCAAK,SAAS;gCAAG;;;;;;;uBA7CzD,SAAS,EAAE;;;;;;;;;;0BAqDtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCACb,qBAAqB,QAAQ,4BAC7B,AAAC,GAAwD,QAAtD,mBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,+BAA9B,uCAAA,iBAAiD,IAAI,EAAC;;;;;;sCAE5D,6LAAC;4BAAK,WAAU;;gCAAU;iCACtB,oBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,+BAA9B,wCAAA,kBAAiD,KAAK;gCAAC;;;;;;;;;;;;;eAXxD;;;;;;;;;;;AAiBb;KAxJM;uCA0JS", "debugId": null}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/store/ProductCard.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Product Card Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { motion } from 'framer-motion';\nimport { Star, ShoppingCart, Heart, Eye } from 'lucide-react';\nimport Image from 'next/image';\n\ninterface ProductCardProps {\n  product: {\n    id: number;\n    name: string;\n    englishName: string;\n    category: string;\n    price: number;\n    originalPrice: number;\n    image: string;\n    brand: string;\n    rating: number;\n    reviews: number;\n    inStock: boolean;\n    features: string[];\n  };\n  onAddToCart: (product: any) => void;\n}\n\nconst ProductCard = ({ product, onAddToCart }: ProductCardProps) => {\n  const discountPercentage = Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n\n  return (\n    <motion.div\n      whileHover={{ y: -8 }}\n      className=\"bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden group\"\n    >\n      {/* Image Container */}\n      <div className=\"relative aspect-square overflow-hidden\">\n        <Image\n          src={product.image}\n          alt={product.name}\n          fill\n          className=\"object-cover transition-transform duration-300 group-hover:scale-110\"\n        />\n        \n        {/* Badges */}\n        <div className=\"absolute top-3 left-3 space-y-2\">\n          {discountPercentage > 0 && (\n            <span className=\"bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold\">\n              -{discountPercentage}%\n            </span>\n          )}\n          {!product.inStock && (\n            <span className=\"bg-gray-500 text-white px-2 py-1 rounded-full text-xs font-bold marathi-text\">\n              स्टॉक नाही\n            </span>\n          )}\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"absolute top-3 right-3 space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n          <button className=\"bg-white/90 p-2 rounded-full hover:bg-white transition-colors duration-200\">\n            <Heart className=\"h-4 w-4 text-gray-600\" />\n          </button>\n          <button className=\"bg-white/90 p-2 rounded-full hover:bg-white transition-colors duration-200\">\n            <Eye className=\"h-4 w-4 text-gray-600\" />\n          </button>\n        </div>\n\n        {/* Quick Add to Cart */}\n        <div className=\"absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n          <button\n            onClick={() => onAddToCart(product)}\n            disabled={!product.inStock}\n            className={`w-full py-2 px-4 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center space-x-2 ${\n              product.inStock\n                ? 'bg-orange-600 text-white hover:bg-orange-700'\n                : 'bg-gray-400 text-gray-200 cursor-not-allowed'\n            }`}\n          >\n            <ShoppingCart className=\"h-4 w-4\" />\n            <span className=\"marathi-text\">\n              {product.inStock ? 'कार्टमध्ये टाका' : 'स्टॉक नाही'}\n            </span>\n          </button>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-4\">\n        {/* Brand */}\n        <div className=\"text-sm text-orange-600 font-semibold mb-1\">\n          {product.brand}\n        </div>\n\n        {/* Title */}\n        <h3 className=\"text-lg font-bold text-gray-900 mb-1 marathi-text line-clamp-1\">\n          {product.name}\n        </h3>\n        <h4 className=\"text-sm font-medium text-gray-600 mb-3 line-clamp-1\">\n          {product.englishName}\n        </h4>\n\n        {/* Rating */}\n        <div className=\"flex items-center space-x-2 mb-3\">\n          <div className=\"flex items-center\">\n            {[...Array(5)].map((_, i) => (\n              <Star\n                key={i}\n                className={`h-4 w-4 ${\n                  i < Math.floor(product.rating)\n                    ? 'text-yellow-400 fill-current'\n                    : 'text-gray-300'\n                }`}\n              />\n            ))}\n          </div>\n          <span className=\"text-sm text-gray-600\">\n            {product.rating} ({product.reviews})\n          </span>\n        </div>\n\n        {/* Features */}\n        <div className=\"mb-4\">\n          <ul className=\"space-y-1\">\n            {product.features.slice(0, 2).map((feature, index) => (\n              <li key={index} className=\"flex items-center text-xs text-gray-600\">\n                <div className=\"w-1.5 h-1.5 bg-orange-600 rounded-full mr-2\"></div>\n                <span className=\"marathi-text\">{feature}</span>\n              </li>\n            ))}\n          </ul>\n        </div>\n\n        {/* Price */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"space-y-1\">\n            <div className=\"text-xl font-bold text-gray-900\">\n              ₹{product.price}\n            </div>\n            {product.originalPrice > product.price && (\n              <div className=\"text-sm text-gray-500 line-through\">\n                ₹{product.originalPrice}\n              </div>\n            )}\n          </div>\n          <div className=\"text-right\">\n            <div className=\"text-xs text-gray-500 marathi-text\">प्रति पीस</div>\n            <div className=\"text-xs text-gray-400\">per piece</div>\n          </div>\n        </div>\n\n        {/* Add to Cart Button */}\n        <button\n          onClick={() => onAddToCart(product)}\n          disabled={!product.inStock}\n          className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2 ${\n            product.inStock\n              ? 'bg-orange-600 text-white hover:bg-orange-700 transform hover:scale-105'\n              : 'bg-gray-400 text-gray-200 cursor-not-allowed'\n          }`}\n        >\n          <ShoppingCart className=\"h-5 w-5\" />\n          <span className=\"marathi-text\">\n            {product.inStock ? 'कार्टमध्ये टाका' : 'स्टॉक नाही'}\n          </span>\n        </button>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default ProductCard;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAwBA,MAAM,cAAc;QAAC,EAAE,OAAO,EAAE,WAAW,EAAoB;IAC7D,MAAM,qBAAqB,KAAK,KAAK,CAAC,AAAC,CAAC,QAAQ,aAAa,GAAG,QAAQ,KAAK,IAAI,QAAQ,aAAa,GAAI;IAE1G,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,KAAK;wBAClB,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,WAAU;;;;;;kCAIZ,6LAAC;wBAAI,WAAU;;4BACZ,qBAAqB,mBACpB,6LAAC;gCAAK,WAAU;;oCAAiE;oCAC7E;oCAAmB;;;;;;;4BAGxB,CAAC,QAAQ,OAAO,kBACf,6LAAC;gCAAK,WAAU;0CAA+E;;;;;;;;;;;;kCAOnG,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAChB,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6LAAC;gCAAO,WAAU;0CAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKnB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS,IAAM,YAAY;4BAC3B,UAAU,CAAC,QAAQ,OAAO;4BAC1B,WAAW,AAAC,uHAIX,OAHC,QAAQ,OAAO,GACX,iDACA;;8CAGN,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,6LAAC;oCAAK,WAAU;8CACb,QAAQ,OAAO,GAAG,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,KAAK;;;;;;kCAIhB,6LAAC;wBAAG,WAAU;kCACX,QAAQ,IAAI;;;;;;kCAEf,6LAAC;wBAAG,WAAU;kCACX,QAAQ,WAAW;;;;;;kCAItB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;wCAEH,WAAW,AAAC,WAIX,OAHC,IAAI,KAAK,KAAK,CAAC,QAAQ,MAAM,IACzB,iCACA;uCAJD;;;;;;;;;;0CASX,6LAAC;gCAAK,WAAU;;oCACb,QAAQ,MAAM;oCAAC;oCAAG,QAAQ,OAAO;oCAAC;;;;;;;;;;;;;kCAKvC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCACX,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC1C,6LAAC;oCAAe,WAAU;;sDACxB,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;mCAFzB;;;;;;;;;;;;;;;kCASf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAAkC;4CAC7C,QAAQ,KAAK;;;;;;;oCAEhB,QAAQ,aAAa,GAAG,QAAQ,KAAK,kBACpC,6LAAC;wCAAI,WAAU;;4CAAqC;4CAChD,QAAQ,aAAa;;;;;;;;;;;;;0CAI7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAK3C,6LAAC;wBACC,SAAS,IAAM,YAAY;wBAC3B,UAAU,CAAC,QAAQ,OAAO;wBAC1B,WAAW,AAAC,oHAIX,OAHC,QAAQ,OAAO,GACX,2EACA;;0CAGN,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,6LAAC;gCAAK,WAAU;0CACb,QAAQ,OAAO,GAAG,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;AAMnD;KA9IM;uCAgJS", "debugId": null}}, {"offset": {"line": 1058, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/store/ProductGrid.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Product Grid Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ProductCard from './ProductCard';\n\n// Sample product data\nconst products = [\n  {\n    id: 1,\n    name: 'मॉड्यूलर स्विच',\n    englishName: 'Modular Switch',\n    category: 'switches-sockets',\n    price: 150,\n    originalPrice: 200,\n    image: '/api/placeholder/300/300',\n    brand: 'Anchor',\n    rating: 4.5,\n    reviews: 128,\n    inStock: true,\n    features: ['6A रेटिंग', 'ISI मार्क', '2 वर्ष हमी']\n  },\n  {\n    id: 2,\n    name: 'LED बल्ब 9W',\n    englishName: 'LED Bulb 9W',\n    category: 'lighting',\n    price: 120,\n    originalPrice: 150,\n    image: '/api/placeholder/300/300',\n    brand: 'Philips',\n    rating: 4.7,\n    reviews: 256,\n    inStock: true,\n    features: ['9W पॉवर', 'वॉर्म व्हाइट', '2 वर्ष हमी']\n  },\n  {\n    id: 3,\n    name: 'सीलिंग फॅन 48\"',\n    englishName: 'Ceiling Fan 48\"',\n    category: 'fans',\n    price: 2500,\n    originalPrice: 3000,\n    image: '/api/placeholder/300/300',\n    brand: 'Bajaj',\n    rating: 4.3,\n    reviews: 89,\n    inStock: true,\n    features: ['48\" साइज', '1200 RPM', '5 वर्ष हमी']\n  },\n  {\n    id: 4,\n    name: 'कॉपर वायर 2.5mm',\n    englishName: 'Copper Wire 2.5mm',\n    category: 'wires-cables',\n    price: 180,\n    originalPrice: 220,\n    image: '/api/placeholder/300/300',\n    brand: 'Havells',\n    rating: 4.6,\n    reviews: 167,\n    inStock: true,\n    features: ['2.5mm थिकनेस', 'ISI मार्क', '99.9% कॉपर']\n  },\n  {\n    id: 5,\n    name: 'सॉकेट 6A',\n    englishName: 'Socket 6A',\n    category: 'switches-sockets',\n    price: 80,\n    originalPrice: 100,\n    image: '/api/placeholder/300/300',\n    brand: 'Anchor',\n    rating: 4.4,\n    reviews: 203,\n    inStock: true,\n    features: ['6A रेटिंग', 'चाइल्ड सेफ्टी', '2 वर्ष हमी']\n  },\n  {\n    id: 6,\n    name: 'टेबल फॅन',\n    englishName: 'Table Fan',\n    category: 'fans',\n    price: 1200,\n    originalPrice: 1500,\n    image: '/api/placeholder/300/300',\n    brand: 'Usha',\n    rating: 4.2,\n    reviews: 145,\n    inStock: false,\n    features: ['3 स्पीड', 'टिल्ट अॅडजस्टमेंट', '2 वर्ष हमी']\n  }\n];\n\ninterface ProductGridProps {\n  selectedCategory: string;\n  onAddToCart: (product: any) => void;\n}\n\nconst ProductGrid = ({ selectedCategory, onAddToCart }: ProductGridProps) => {\n  const [filteredProducts, setFilteredProducts] = useState(products);\n\n  useEffect(() => {\n    if (selectedCategory === 'all') {\n      setFilteredProducts(products);\n    } else {\n      setFilteredProducts(products.filter(product => product.category === selectedCategory));\n    }\n  }, [selectedCategory]);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6\n      }\n    }\n  };\n\n  return (\n    <div>\n      {/* Results Header */}\n      <div className=\"flex justify-between items-center mb-8\">\n        <div>\n          <h3 className=\"text-xl font-bold text-gray-900 marathi-text\">\n            उत्पादने ({filteredProducts.length})\n          </h3>\n          <p className=\"text-gray-600\">\n            Products ({filteredProducts.length} items)\n          </p>\n        </div>\n        \n        <select className=\"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\">\n          <option value=\"popular\">लोकप्रिय</option>\n          <option value=\"price-low\">किंमत: कमी ते जास्त</option>\n          <option value=\"price-high\">किंमत: जास्त ते कमी</option>\n          <option value=\"rating\">रेटिंग</option>\n        </select>\n      </div>\n\n      {/* Products Grid */}\n      <motion.div\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n        className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\"\n      >\n        <AnimatePresence mode=\"wait\">\n          {filteredProducts.map((product) => (\n            <motion.div\n              key={product.id}\n              variants={itemVariants}\n              layout\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 0.8 }}\n              transition={{ duration: 0.3 }}\n            >\n              <ProductCard\n                product={product}\n                onAddToCart={onAddToCart}\n              />\n            </motion.div>\n          ))}\n        </AnimatePresence>\n      </motion.div>\n\n      {/* No Products Found */}\n      {filteredProducts.length === 0 && (\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-center py-16\"\n        >\n          <div className=\"text-gray-400 mb-4\">\n            <svg className=\"w-16 h-16 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3\" />\n            </svg>\n          </div>\n          <h3 className=\"text-xl font-semibold text-gray-600 mb-2 marathi-text\">\n            या श्रेणीमध्ये कोणतेही उत्पादन सापडले नाही\n          </h3>\n          <p className=\"text-gray-500\">\n            No products found in this category\n          </p>\n        </motion.div>\n      )}\n\n      {/* Load More Button */}\n      {filteredProducts.length > 0 && (\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5 }}\n          className=\"text-center mt-12\"\n        >\n          <button className=\"bg-orange-600 text-white px-8 py-3 rounded-full font-semibold hover:bg-orange-700 transition-colors duration-200\">\n            <span className=\"marathi-text\">अधिक उत्पादने लोड करा</span>\n          </button>\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\nexport default ProductGrid;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AAAA;AACA;;;AAJA;;;;AAMA,sBAAsB;AACtB,MAAM,WAAW;IACf;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,eAAe;QACf,OAAO;QACP,OAAO;QACP,QAAQ;QACR,SAAS;QACT,SAAS;QACT,UAAU;YAAC;YAAa;YAAa;SAAa;IACpD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,eAAe;QACf,OAAO;QACP,OAAO;QACP,QAAQ;QACR,SAAS;QACT,SAAS;QACT,UAAU;YAAC;YAAW;YAAgB;SAAa;IACrD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,eAAe;QACf,OAAO;QACP,OAAO;QACP,QAAQ;QACR,SAAS;QACT,SAAS;QACT,UAAU;YAAC;YAAY;YAAY;SAAa;IAClD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,eAAe;QACf,OAAO;QACP,OAAO;QACP,QAAQ;QACR,SAAS;QACT,SAAS;QACT,UAAU;YAAC;YAAgB;YAAa;SAAa;IACvD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,eAAe;QACf,OAAO;QACP,OAAO;QACP,QAAQ;QACR,SAAS;QACT,SAAS;QACT,UAAU;YAAC;YAAa;YAAiB;SAAa;IACxD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,eAAe;QACf,OAAO;QACP,OAAO;QACP,QAAQ;QACR,SAAS;QACT,SAAS;QACT,UAAU;YAAC;YAAW;YAAqB;SAAa;IAC1D;CACD;AAOD,MAAM,cAAc;QAAC,EAAE,gBAAgB,EAAE,WAAW,EAAoB;;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,qBAAqB,OAAO;gBAC9B,oBAAoB;YACtB,OAAO;gBACL,oBAAoB,SAAS,MAAM;6CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;;YACtE;QACF;gCAAG;QAAC;KAAiB;IAErB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,6LAAC;;0BAEC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;oCAA+C;oCAChD,iBAAiB,MAAM;oCAAC;;;;;;;0CAErC,6LAAC;gCAAE,WAAU;;oCAAgB;oCAChB,iBAAiB,MAAM;oCAAC;;;;;;;;;;;;;kCAIvC,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC;gCAAO,OAAM;0CAAU;;;;;;0CACxB,6LAAC;gCAAO,OAAM;0CAAY;;;;;;0CAC1B,6LAAC;gCAAO,OAAM;0CAAa;;;;;;0CAC3B,6LAAC;gCAAO,OAAM;0CAAS;;;;;;;;;;;;;;;;;;0BAK3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,WAAU;0BAEV,cAAA,6LAAC,4LAAA,CAAA,kBAAe;oBAAC,MAAK;8BACnB,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,MAAM;4BACN,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAC/B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,6LAAC,6IAAA,CAAA,UAAW;gCACV,SAAS;gCACT,aAAa;;;;;;2BAVV,QAAQ,EAAE;;;;;;;;;;;;;;;YAkBtB,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAoB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC3E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,6LAAC;wBAAG,WAAU;kCAAwD;;;;;;kCAGtE,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;YAOhC,iBAAiB,MAAM,GAAG,mBACzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;0BAEV,cAAA,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;;;;;;;;;;;AAM3C;GApHM;KAAA;uCAsHS", "debugId": null}}, {"offset": {"line": 1474, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/store/ShoppingCart.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Shopping Cart Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { X, Plus, Minus, Trash2, ShoppingBag } from 'lucide-react';\nimport Image from 'next/image';\n\ninterface ShoppingCartProps {\n  isOpen: boolean;\n  onClose: () => void;\n  items: any[];\n  onRemoveItem: (id: number) => void;\n  onUpdateQuantity: (id: number, quantity: number) => void;\n}\n\nconst ShoppingCart = ({ isOpen, onClose, items, onRemoveItem, onUpdateQuantity }: ShoppingCartProps) => {\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  const totalAmount = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);\n  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          className=\"fixed inset-0 z-50 flex\"\n          onClick={onClose}\n        >\n          {/* Backdrop */}\n          <div className=\"absolute inset-0 bg-black/50 backdrop-blur-sm\" />\n\n          {/* Cart Panel */}\n          <motion.div\n            initial={{ x: '100%' }}\n            animate={{ x: 0 }}\n            exit={{ x: '100%' }}\n            transition={{ type: \"spring\", damping: 25, stiffness: 300 }}\n            className=\"relative ml-auto bg-white w-full max-w-md h-full shadow-2xl flex flex-col\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            {/* Header */}\n            <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n              <div>\n                <h2 className=\"text-xl font-bold text-gray-900 marathi-text\">\n                  शॉपिंग कार्ट\n                </h2>\n                <p className=\"text-sm text-gray-600\">\n                  Shopping Cart ({totalItems} items)\n                </p>\n              </div>\n              <button\n                onClick={onClose}\n                className=\"p-2 hover:bg-gray-100 rounded-full transition-colors duration-200\"\n              >\n                <X className=\"h-6 w-6 text-gray-600\" />\n              </button>\n            </div>\n\n            {/* Cart Items */}\n            <div className=\"flex-1 overflow-y-auto p-6\">\n              {items.length === 0 ? (\n                <div className=\"text-center py-16\">\n                  <ShoppingBag className=\"h-16 w-16 text-gray-300 mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-semibold text-gray-600 mb-2 marathi-text\">\n                    कार्ट रिकामी आहे\n                  </h3>\n                  <p className=\"text-gray-500\">\n                    Your cart is empty\n                  </p>\n                </div>\n              ) : (\n                <div className=\"space-y-4\">\n                  {items.map((item) => (\n                    <motion.div\n                      key={item.id}\n                      layout\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      exit={{ opacity: 0, y: -20 }}\n                      className=\"flex items-center space-x-4 bg-gray-50 rounded-lg p-4\"\n                    >\n                      {/* Product Image */}\n                      <div className=\"relative w-16 h-16 rounded-lg overflow-hidden\">\n                        <Image\n                          src={item.image}\n                          alt={item.name}\n                          fill\n                          className=\"object-cover\"\n                        />\n                      </div>\n\n                      {/* Product Info */}\n                      <div className=\"flex-1 min-w-0\">\n                        <h4 className=\"text-sm font-semibold text-gray-900 marathi-text truncate\">\n                          {item.name}\n                        </h4>\n                        <p className=\"text-xs text-gray-600 truncate\">\n                          {item.englishName}\n                        </p>\n                        <p className=\"text-sm font-bold text-orange-600\">\n                          ₹{item.price}\n                        </p>\n                      </div>\n\n                      {/* Quantity Controls */}\n                      <div className=\"flex items-center space-x-2\">\n                        <button\n                          onClick={() => onUpdateQuantity(item.id, item.quantity - 1)}\n                          className=\"p-1 hover:bg-gray-200 rounded-full transition-colors duration-200\"\n                        >\n                          <Minus className=\"h-4 w-4 text-gray-600\" />\n                        </button>\n                        <span className=\"w-8 text-center font-semibold\">\n                          {item.quantity}\n                        </span>\n                        <button\n                          onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}\n                          className=\"p-1 hover:bg-gray-200 rounded-full transition-colors duration-200\"\n                        >\n                          <Plus className=\"h-4 w-4 text-gray-600\" />\n                        </button>\n                      </div>\n\n                      {/* Remove Button */}\n                      <button\n                        onClick={() => onRemoveItem(item.id)}\n                        className=\"p-2 hover:bg-red-100 rounded-full transition-colors duration-200\"\n                      >\n                        <Trash2 className=\"h-4 w-4 text-red-600\" />\n                      </button>\n                    </motion.div>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Footer */}\n            {items.length > 0 && (\n              <div className=\"border-t border-gray-200 p-6 space-y-4\">\n                {/* Total */}\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-lg font-semibold text-gray-900 marathi-text\">\n                    एकूण रक्कम:\n                  </span>\n                  <span className=\"text-xl font-bold text-orange-600\">\n                    ₹{totalAmount}\n                  </span>\n                </div>\n\n                {/* Checkout Button */}\n                <button className=\"w-full bg-orange-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-orange-700 transition-colors duration-200\">\n                  <span className=\"marathi-text\">चेकआउट करा</span>\n                </button>\n\n                {/* Continue Shopping */}\n                <button\n                  onClick={onClose}\n                  className=\"w-full border-2 border-orange-600 text-orange-600 py-3 px-4 rounded-lg font-semibold hover:bg-orange-50 transition-colors duration-200\"\n                >\n                  <span className=\"marathi-text\">खरेदी सुरू ठेवा</span>\n                </button>\n              </div>\n            )}\n          </motion.div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default ShoppingCart;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAeA,MAAM,eAAe;QAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,gBAAgB,EAAqB;;IACjG,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,QAAQ;gBACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;0CAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;iCAAG;QAAC;KAAO;IAEX,MAAM,cAAc,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAG;IACpF,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;IAEpE,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;YACV,SAAS;;8BAGT,6LAAC;oBAAI,WAAU;;;;;;8BAGf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;oBAAO;oBACrB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG;oBAAO;oBAClB,YAAY;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBAC1D,WAAU;oBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;sCAGjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAG7D,6LAAC;4CAAE,WAAU;;gDAAwB;gDACnB;gDAAW;;;;;;;;;;;;;8CAG/B,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,6LAAC;4BAAI,WAAU;sCACZ,MAAM,MAAM,KAAK,kBAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;yFAK/B,6LAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,MAAM;wCACN,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC3B,WAAU;;0DAGV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,KAAK,KAAK;oDACf,KAAK,KAAK,IAAI;oDACd,IAAI;oDACJ,WAAU;;;;;;;;;;;0DAKd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,KAAK,IAAI;;;;;;kEAEZ,6LAAC;wDAAE,WAAU;kEACV,KAAK,WAAW;;;;;;kEAEnB,6LAAC;wDAAE,WAAU;;4DAAoC;4DAC7C,KAAK,KAAK;;;;;;;;;;;;;0DAKhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;wDACzD,WAAU;kEAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,6LAAC;wDAAK,WAAU;kEACb,KAAK,QAAQ;;;;;;kEAEhB,6LAAC;wDACC,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;wDACzD,WAAU;kEAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKpB,6LAAC;gDACC,SAAS,IAAM,aAAa,KAAK,EAAE;gDACnC,WAAU;0DAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;uCAtDf,KAAK,EAAE;;;;;;;;;;;;;;;wBA+DrB,MAAM,MAAM,GAAG,mBACd,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAmD;;;;;;sDAGnE,6LAAC;4CAAK,WAAU;;gDAAoC;gDAChD;;;;;;;;;;;;;8CAKN,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;8CAIjC,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD;GAtKM;KAAA;uCAwKS", "debugId": null}}, {"offset": {"line": 1892, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/app/store/page.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Store Page\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { useState } from 'react';\nimport StoreHero from '@/components/store/StoreHero';\nimport ProductCategories from '@/components/store/ProductCategories';\nimport ProductGrid from '@/components/store/ProductGrid';\nimport ShoppingCart from '@/components/store/ShoppingCart';\n\n// Define the cart item type\ninterface CartItem {\n  id: number;\n  name: string;\n  price: number;\n  image: string;\n  quantity: number;\n}\n\nexport default function StorePage() {\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [cartItems, setCartItems] = useState<CartItem[]>([]);\n  const [isCartOpen, setIsCartOpen] = useState(false);\n\n  const addToCart = (product: Omit<CartItem, 'quantity'>) => {\n    setCartItems(prev => {\n      const existingItem = prev.find(item => item.id === product.id);\n      if (existingItem) {\n        return prev.map(item =>\n          item.id === product.id\n            ? { ...item, quantity: item.quantity + 1 }\n            : item\n        );\n      }\n      return [...prev, { ...product, quantity: 1 }];\n    });\n  };\n\n  const removeFromCart = (productId: number) => {\n    setCartItems(prev => prev.filter(item => item.id !== productId));\n  };\n\n  const updateQuantity = (productId: number, quantity: number) => {\n    if (quantity === 0) {\n      removeFromCart(productId);\n      return;\n    }\n    setCartItems(prev =>\n      prev.map(item =>\n        item.id === productId ? { ...item, quantity } : item\n      )\n    );\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <StoreHero cartItemsCount={cartItems.length} onCartClick={() => setIsCartOpen(true)} />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <ProductCategories \n          selectedCategory={selectedCategory}\n          onCategoryChange={setSelectedCategory}\n        />\n        \n        <ProductGrid \n          selectedCategory={selectedCategory}\n          onAddToCart={addToCart}\n        />\n      </div>\n\n      <ShoppingCart\n        isOpen={isCartOpen}\n        onClose={() => setIsCartOpen(false)}\n        items={cartItems}\n        onRemoveItem={removeFromCart}\n        onUpdateQuantity={updateQuantity}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAiBe,SAAS;;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,YAAY,CAAC;QACjB,aAAa,CAAA;YACX,MAAM,eAAe,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,QAAQ,EAAE;YAC7D,IAAI,cAAc;gBAChB,OAAO,KAAK,GAAG,CAAC,CAAA,OACd,KAAK,EAAE,KAAK,QAAQ,EAAE,GAClB;wBAAE,GAAG,IAAI;wBAAE,UAAU,KAAK,QAAQ,GAAG;oBAAE,IACvC;YAER;YACA,OAAO;mBAAI;gBAAM;oBAAE,GAAG,OAAO;oBAAE,UAAU;gBAAE;aAAE;QAC/C;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACvD;IAEA,MAAM,iBAAiB,CAAC,WAAmB;QACzC,IAAI,aAAa,GAAG;YAClB,eAAe;YACf;QACF;QACA,aAAa,CAAA,OACX,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,YAAY;oBAAE,GAAG,IAAI;oBAAE;gBAAS,IAAI;IAGtD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,2IAAA,CAAA,UAAS;gBAAC,gBAAgB,UAAU,MAAM;gBAAE,aAAa,IAAM,cAAc;;;;;;0BAE9E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mJAAA,CAAA,UAAiB;wBAChB,kBAAkB;wBAClB,kBAAkB;;;;;;kCAGpB,6LAAC,6IAAA,CAAA,UAAW;wBACV,kBAAkB;wBAClB,aAAa;;;;;;;;;;;;0BAIjB,6LAAC,8IAAA,CAAA,UAAY;gBACX,QAAQ;gBACR,SAAS,IAAM,cAAc;gBAC7B,OAAO;gBACP,cAAc;gBACd,kBAAkB;;;;;;;;;;;;AAI1B;GA5DwB;KAAA", "debugId": null}}]}