{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/app/gallery/layout.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Gallery Layout\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\nimport { Metadata } from 'next';\n\nexport const metadata: Metadata = {\n  title: 'आमचे काम - Chava Electricals Gallery',\n  description: 'चावा इलेक्ट्रिकल्सच्या पूर्ण केलेल्या प्रकल्पांचे फोटो आणि व्हिडिओ पहा. घरगुती वायरिंग, सेन्सर लाइटिंग आणि इतर इलेक्ट्रिकल कामांचे उदाहरण.',\n  keywords: 'electrical work gallery, home wiring photos, sensor lighting examples, Maharashtra electrical services',\n};\n\nexport default function GalleryLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return children;\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAIM,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;AACZ;AAEe,SAAS,cAAc,EACpC,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}