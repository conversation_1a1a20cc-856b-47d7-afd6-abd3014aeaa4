/*
 * Chava Electricals Website - Product Grid Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ProductCard from './ProductCard';

// Sample product data
const products = [
  {
    id: 1,
    name: 'मॉड्यूलर स्विच',
    englishName: 'Modular Switch',
    category: 'switches-sockets',
    price: 150,
    originalPrice: 200,
    image: '/api/placeholder/300/300',
    brand: 'Anchor',
    rating: 4.5,
    reviews: 128,
    inStock: true,
    features: ['6A रेटिंग', 'ISI मार्क', '2 वर्ष हमी']
  },
  {
    id: 2,
    name: 'LED बल्ब 9W',
    englishName: 'LED Bulb 9W',
    category: 'lighting',
    price: 120,
    originalPrice: 150,
    image: '/api/placeholder/300/300',
    brand: 'Philips',
    rating: 4.7,
    reviews: 256,
    inStock: true,
    features: ['9W पॉवर', 'वॉर्म व्हाइट', '2 वर्ष हमी']
  },
  {
    id: 3,
    name: 'सीलिंग फॅन 48"',
    englishName: 'Ceiling Fan 48"',
    category: 'fans',
    price: 2500,
    originalPrice: 3000,
    image: '/api/placeholder/300/300',
    brand: 'Bajaj',
    rating: 4.3,
    reviews: 89,
    inStock: true,
    features: ['48" साइज', '1200 RPM', '5 वर्ष हमी']
  },
  {
    id: 4,
    name: 'कॉपर वायर 2.5mm',
    englishName: 'Copper Wire 2.5mm',
    category: 'wires-cables',
    price: 180,
    originalPrice: 220,
    image: '/api/placeholder/300/300',
    brand: 'Havells',
    rating: 4.6,
    reviews: 167,
    inStock: true,
    features: ['2.5mm थिकनेस', 'ISI मार्क', '99.9% कॉपर']
  },
  {
    id: 5,
    name: 'सॉकेट 6A',
    englishName: 'Socket 6A',
    category: 'switches-sockets',
    price: 80,
    originalPrice: 100,
    image: '/api/placeholder/300/300',
    brand: 'Anchor',
    rating: 4.4,
    reviews: 203,
    inStock: true,
    features: ['6A रेटिंग', 'चाइल्ड सेफ्टी', '2 वर्ष हमी']
  },
  {
    id: 6,
    name: 'टेबल फॅन',
    englishName: 'Table Fan',
    category: 'fans',
    price: 1200,
    originalPrice: 1500,
    image: '/api/placeholder/300/300',
    brand: 'Usha',
    rating: 4.2,
    reviews: 145,
    inStock: false,
    features: ['3 स्पीड', 'टिल्ट अॅडजस्टमेंट', '2 वर्ष हमी']
  }
];

interface ProductGridProps {
  selectedCategory: string;
  onAddToCart: (product: any) => void;
}

const ProductGrid = ({ selectedCategory, onAddToCart }: ProductGridProps) => {
  const [filteredProducts, setFilteredProducts] = useState(products);

  useEffect(() => {
    if (selectedCategory === 'all') {
      setFilteredProducts(products);
    } else {
      setFilteredProducts(products.filter(product => product.category === selectedCategory));
    }
  }, [selectedCategory]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <div>
      {/* Results Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h3 className="text-xl font-bold text-gray-900 marathi-text">
            उत्पादने ({filteredProducts.length})
          </h3>
          <p className="text-gray-600">
            Products ({filteredProducts.length} items)
          </p>
        </div>
        
        <select className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
          <option value="popular">लोकप्रिय</option>
          <option value="price-low">किंमत: कमी ते जास्त</option>
          <option value="price-high">किंमत: जास्त ते कमी</option>
          <option value="rating">रेटिंग</option>
        </select>
      </div>

      {/* Products Grid */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
      >
        <AnimatePresence mode="wait">
          {filteredProducts.map((product) => (
            <motion.div
              key={product.id}
              variants={itemVariants}
              layout
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.3 }}
            >
              <ProductCard
                product={product}
                onAddToCart={onAddToCart}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>

      {/* No Products Found */}
      {filteredProducts.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-16"
        >
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-gray-600 mb-2 marathi-text">
            या श्रेणीमध्ये कोणतेही उत्पादन सापडले नाही
          </h3>
          <p className="text-gray-500">
            No products found in this category
          </p>
        </motion.div>
      )}

      {/* Load More Button */}
      {filteredProducts.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="text-center mt-12"
        >
          <button className="bg-orange-600 text-white px-8 py-3 rounded-full font-semibold hover:bg-orange-700 transition-colors duration-200">
            <span className="marathi-text">अधिक उत्पादने लोड करा</span>
          </button>
        </motion.div>
      )}
    </div>
  );
};

export default ProductGrid;
