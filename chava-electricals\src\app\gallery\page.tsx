/*
 * Chava Electricals Website - Gallery Page
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { useState } from 'react';
import GalleryHero from '@/components/gallery/GalleryHero';
import GalleryFilter from '@/components/gallery/GalleryFilter';
import GalleryGrid from '@/components/gallery/GalleryGrid';

export default function GalleryPage() {
  const [activeFilter, setActiveFilter] = useState('all');
  const [activeMediaType, setActiveMediaType] = useState('all');

  return (
    <div className="min-h-screen bg-gray-50">
      <GalleryHero />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <GalleryFilter
          onFilterChange={setActiveFilter}
          onMediaTypeChange={setActiveMediaType}
        />
        <GalleryGrid
          activeFilter={activeFilter}
          activeMediaType={activeMediaType}
        />
      </div>
    </div>
  );
}
