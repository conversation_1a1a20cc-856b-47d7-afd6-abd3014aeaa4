@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

/*
 * Chava Electricals Website - Global Styles
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary-orange: #ea580c;
  --primary-yellow: #eab308;
  --maharashtra-saffron: #ff9933;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', 'Noto Sans Devanagari', sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-orange);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #c2410c;
}

/* Marathi text styling */
.marathi-text {
  font-family: 'Noto Sans Devanagari', sans-serif;
  font-weight: 500;
}

/* Electrical theme animations */
@keyframes spark {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

.spark-animation {
  animation: spark 2s ease-in-out infinite;
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px var(--primary-orange); }
  50% { box-shadow: 0 0 20px var(--primary-orange), 0 0 30px var(--primary-orange); }
}

.glow-animation {
  animation: glow 3s ease-in-out infinite;
}

/* Gradient backgrounds */
.maharashtra-gradient {
  background: linear-gradient(135deg, #ff9933 0%, #ffffff 50%, #138808 100%);
}

.electrical-gradient {
  background: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-yellow) 100%);
}

/* Custom utilities */
.bg-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(234, 88, 12, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(234, 179, 8, 0.1) 0%, transparent 50%);
}
