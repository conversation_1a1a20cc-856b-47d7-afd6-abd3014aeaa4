/*
 * Chava Electricals Website - Gallery Filter Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Home, 
  Lightbulb, 
  Wrench, 
  Fan, 
  Zap, 
  Shield,
  Grid3X3,
  Camera,
  Video
} from 'lucide-react';

interface GalleryFilterProps {
  onFilterChange?: (filter: string) => void;
  onMediaTypeChange?: (type: string) => void;
}

const GalleryFilter = ({ onFilterChange, onMediaTypeChange }: GalleryFilterProps) => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [activeMediaType, setActiveMediaType] = useState('all');

  const categories = [
    {
      id: 'all',
      icon: <Grid3X3 className="h-5 w-5" />,
      label: 'सर्व काम',
      english: 'All Work',
      count: 45
    },
    {
      id: 'home-wiring',
      icon: <Home className="h-5 w-5" />,
      label: 'घरगुती वायरिंग',
      english: 'Home Wiring',
      count: 15
    },
    {
      id: 'sensor-lighting',
      icon: <Lightbulb className="h-5 w-5" />,
      label: 'सेन्सर लाइटिंग',
      english: 'Sensor Lighting',
      count: 12
    },
    {
      id: 'repairs',
      icon: <Wrench className="h-5 w-5" />,
      label: 'रिपेअर सर्विस',
      english: 'Repair Services',
      count: 8
    },
    {
      id: 'fan-installation',
      icon: <Fan className="h-5 w-5" />,
      label: 'फॅन इन्स्टॉलेशन',
      english: 'Fan Installation',
      count: 6
    },
    {
      id: 'switch-board',
      icon: <Zap className="h-5 w-5" />,
      label: 'स्विच बोर्ड',
      english: 'Switch Board',
      count: 4
    }
  ];

  const mediaTypes = [
    {
      id: 'all',
      icon: <Grid3X3 className="h-4 w-4" />,
      label: 'सर्व',
      english: 'All'
    },
    {
      id: 'photos',
      icon: <Camera className="h-4 w-4" />,
      label: 'फोटो',
      english: 'Photos'
    },
    {
      id: 'videos',
      icon: <Video className="h-4 w-4" />,
      label: 'व्हिडिओ',
      english: 'Videos'
    }
  ];

  const handleFilterChange = (filterId: string) => {
    setActiveFilter(filterId);
    onFilterChange?.(filterId);
  };

  const handleMediaTypeChange = (typeId: string) => {
    setActiveMediaType(typeId);
    onMediaTypeChange?.(typeId);
  };

  return (
    <div className="mb-12">
      {/* Category Filters */}
      <div className="mb-8">
        <motion.h3
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-2xl font-bold text-gray-900 mb-6 text-center marathi-text"
        >
          काम प्रकार निवडा
        </motion.h3>
        
        <div className="flex flex-wrap justify-center gap-3">
          {categories.map((category, index) => (
            <motion.button
              key={category.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => handleFilterChange(category.id)}
              className={`flex items-center space-x-2 px-4 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 ${
                activeFilter === category.id
                  ? 'bg-orange-600 text-white shadow-lg'
                  : 'bg-white text-gray-700 hover:bg-orange-50 hover:text-orange-600 shadow-md'
              }`}
            >
              <span className={activeFilter === category.id ? 'text-white' : 'text-orange-600'}>
                {category.icon}
              </span>
              <div className="text-left">
                <div className="text-sm marathi-text">{category.label}</div>
                <div className="text-xs opacity-75">{category.english}</div>
              </div>
              <span className={`text-xs px-2 py-1 rounded-full ${
                activeFilter === category.id
                  ? 'bg-white/20 text-white'
                  : 'bg-orange-100 text-orange-600'
              }`}>
                {category.count}
              </span>
            </motion.button>
          ))}
        </div>
      </div>

      {/* Media Type Filters */}
      <div className="flex justify-center">
        <div className="bg-white rounded-full p-2 shadow-lg">
          <div className="flex space-x-2">
            {mediaTypes.map((type, index) => (
              <motion.button
                key={type.id}
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.5 + index * 0.1 }}
                onClick={() => handleMediaTypeChange(type.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-full font-medium transition-all duration-300 ${
                  activeMediaType === type.id
                    ? 'bg-orange-600 text-white'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {type.icon}
                <div className="text-center">
                  <div className="text-sm marathi-text">{type.label}</div>
                  <div className="text-xs opacity-75">{type.english}</div>
                </div>
              </motion.button>
            ))}
          </div>
        </div>
      </div>

      {/* Active Filters Display */}
      {(activeFilter !== 'all' || activeMediaType !== 'all') && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-6 text-center"
        >
          <div className="inline-flex items-center space-x-2 bg-orange-100 text-orange-800 px-4 py-2 rounded-full">
            <span className="text-sm marathi-text">सक्रिय फिल्टर:</span>
            {activeFilter !== 'all' && (
              <span className="bg-orange-200 px-2 py-1 rounded text-xs marathi-text">
                {categories.find(c => c.id === activeFilter)?.label}
              </span>
            )}
            {activeMediaType !== 'all' && (
              <span className="bg-orange-200 px-2 py-1 rounded text-xs marathi-text">
                {mediaTypes.find(t => t.id === activeMediaType)?.label}
              </span>
            )}
            <button
              onClick={() => {
                setActiveFilter('all');
                setActiveMediaType('all');
                onFilterChange?.('all');
                onMediaTypeChange?.('all');
              }}
              className="text-orange-600 hover:text-orange-800 text-xs underline"
            >
              साफ करा
            </button>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default GalleryFilter;
