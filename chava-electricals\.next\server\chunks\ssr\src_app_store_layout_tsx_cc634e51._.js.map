{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/app/store/layout.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Store Layout\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\nimport { Metadata } from 'next';\n\nexport const metadata: Metadata = {\n  title: 'ऑनलाइन स्टोअर - Chava Electricals Shop',\n  description: 'चावा इलेक्ट्रिकल्सच्या ऑनलाइन स्टोअरमधून इलेक्ट्रिकल उत्पादने खरेदी करा. स्विच, सॉकेट, वायर, फॅन आणि इतर इलेक्ट्रिकल सामान.',\n  keywords: 'electrical products online, switches, sockets, wires, fans, electrical accessories, Maharashtra',\n};\n\nexport default function StoreLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return children;\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAIM,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;AACZ;AAEe,SAAS,YAAY,EAClC,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}