/*
 * Chava Electricals Website - Gallery Item Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { motion } from 'framer-motion';
import { Play, Camera, MapPin, Calendar, Eye } from 'lucide-react';
import Image from 'next/image';

interface GalleryItemProps {
  item: {
    id: number;
    type: 'photo' | 'video';
    category: string;
    title: string;
    englishTitle: string;
    description: string;
    englishDescription: string;
    location: string;
    date: string;
    image: string;
    thumbnail: string;
    videoUrl?: string;
  };
  onClick: () => void;
}

const GalleryItem = ({ item, onClick }: GalleryItemProps) => {
  const getCategoryBadge = (category: string) => {
    const badges = {
      'home-wiring': { label: 'घरगुती वायरिंग', color: 'bg-blue-500' },
      'sensor-lighting': { label: 'सेन्सर लाइटिंग', color: 'bg-yellow-500' },
      'repairs': { label: 'रिपेअर सर्विस', color: 'bg-red-500' },
      'fan-installation': { label: 'फॅन इन्स्टॉलेशन', color: 'bg-green-500' },
      'switch-board': { label: 'स्विच बोर्ड', color: 'bg-purple-500' },
    };
    return badges[category] || { label: category, color: 'bg-gray-500' };
  };

  const categoryBadge = getCategoryBadge(item.category);

  return (
    <motion.div
      whileHover={{ y: -8 }}
      whileTap={{ scale: 0.98 }}
      className="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden cursor-pointer group"
      onClick={onClick}
    >
      {/* Image Container */}
      <div className="relative aspect-[4/3] overflow-hidden">
        <Image
          src={item.thumbnail}
          alt={item.title}
          fill
          className="object-cover transition-transform duration-300 group-hover:scale-110"
        />
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-all duration-300 flex items-center justify-center">
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            whileHover={{ scale: 1, opacity: 1 }}
            className="bg-white/90 backdrop-blur-sm rounded-full p-4"
          >
            {item.type === 'video' ? (
              <Play className="h-8 w-8 text-orange-600" />
            ) : (
              <Eye className="h-8 w-8 text-orange-600" />
            )}
          </motion.div>
        </div>

        {/* Type Badge */}
        <div className="absolute top-4 left-4">
          <div className={`flex items-center space-x-1 px-3 py-1 rounded-full text-white text-xs font-semibold ${
            item.type === 'video' ? 'bg-red-500' : 'bg-blue-500'
          }`}>
            {item.type === 'video' ? (
              <Play className="h-3 w-3" />
            ) : (
              <Camera className="h-3 w-3" />
            )}
            <span>{item.type === 'video' ? 'व्हिडिओ' : 'फोटो'}</span>
          </div>
        </div>

        {/* Category Badge */}
        <div className="absolute top-4 right-4">
          <div className={`px-3 py-1 rounded-full text-white text-xs font-semibold ${categoryBadge.color}`}>
            {categoryBadge.label}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Title */}
        <h3 className="text-lg font-bold text-gray-900 mb-2 marathi-text group-hover:text-orange-600 transition-colors duration-200">
          {item.title}
        </h3>
        <h4 className="text-md font-semibold text-orange-600 mb-3">
          {item.englishTitle}
        </h4>

        {/* Description */}
        <p className="text-gray-600 text-sm mb-4 marathi-text line-clamp-2">
          {item.description}
        </p>
        <p className="text-gray-500 text-xs mb-4 line-clamp-2">
          {item.englishDescription}
        </p>

        {/* Meta Information */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-1">
            <MapPin className="h-3 w-3" />
            <span className="marathi-text">{item.location}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Calendar className="h-3 w-3" />
            <span>{new Date(item.date).toLocaleDateString('mr-IN')}</span>
          </div>
        </div>

        {/* View Button */}
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="mt-4 pt-4 border-t border-gray-100"
        >
          <button className="w-full bg-orange-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-orange-700 transition-colors duration-200 flex items-center justify-center space-x-2">
            <Eye className="h-4 w-4" />
            <span className="marathi-text">विस्तार पहा</span>
          </button>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default GalleryItem;
