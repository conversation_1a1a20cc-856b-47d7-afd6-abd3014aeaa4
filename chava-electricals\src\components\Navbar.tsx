/*
 * Chava Electricals Website - Navigation Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Menu, X, Zap, Phone } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);

  const navItems = [
    { href: '/', label: 'मुख्यपृष्ठ', english: 'Home' },
    { href: '/services', label: 'सेवा', english: 'Services' },
    { href: '/gallery', label: 'आमचे काम', english: 'Our Work' },
    { href: '/store', label: 'ऑनलाइन स्टोअर', english: 'Online Store' },
    { href: '/about', label: 'आमच्याबद्दल', english: 'About' },
    { href: '/contact', label: 'संपर्क', english: 'Contact' },
  ];

  return (
    <nav className="bg-gradient-to-r from-orange-600 via-orange-500 to-yellow-500 shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="bg-white p-2 rounded-full">
              <Zap className="h-6 w-6 text-orange-600" />
            </div>
            <div className="text-white">
              <h1 className="text-xl font-bold">Chava Electricals</h1>
              <p className="text-xs text-orange-100">चावा इलेक्ट्रिकल्स</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="text-white hover:text-orange-100 transition-colors duration-200 font-medium group"
              >
                <div className="text-center">
                  <div className="text-sm">{item.label}</div>
                  <div className="text-xs text-orange-100 group-hover:text-white">
                    {item.english}
                  </div>
                </div>
              </Link>
            ))}
            
            {/* Call Button */}
            <Link
              href="tel:+919876543210"
              className="bg-white text-orange-600 px-4 py-2 rounded-full font-semibold hover:bg-orange-50 transition-colors duration-200 flex items-center space-x-2"
            >
              <Phone className="h-4 w-4" />
              <span>कॉल करा</span>
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-white hover:text-orange-100 transition-colors duration-200"
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-orange-700"
          >
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="block px-3 py-2 text-white hover:bg-orange-600 rounded-md transition-colors duration-200"
                  onClick={() => setIsOpen(false)}
                >
                  <div>{item.label}</div>
                  <div className="text-xs text-orange-200">{item.english}</div>
                </Link>
              ))}
              <Link
                href="tel:+919876543210"
                className="block px-3 py-2 bg-white text-orange-600 rounded-md font-semibold hover:bg-orange-50 transition-colors duration-200 flex items-center space-x-2"
                onClick={() => setIsOpen(false)}
              >
                <Phone className="h-4 w-4" />
                <span>कॉल करा</span>
              </Link>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
};

export default Navbar;
