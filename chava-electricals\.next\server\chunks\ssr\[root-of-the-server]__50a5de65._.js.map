{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/Navbar.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Navigation Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, Zap, Phone } from 'lucide-react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst Navbar = () => {\n  const [isOpen, setIsOpen] = useState(false);\n\n  const navItems = [\n    { href: '/', label: 'मुख्यपृष्ठ', english: 'Home' },\n    { href: '/services', label: 'सेवा', english: 'Services' },\n    { href: '/gallery', label: 'आमचे काम', english: 'Our Work' },\n    { href: '/store', label: 'ऑनलाइन स्टोअर', english: 'Online Store' },\n    { href: '/about', label: 'आमच्याबद्दल', english: 'About' },\n    { href: '/contact', label: 'संपर्क', english: 'Contact' },\n  ];\n\n  return (\n    <nav className=\"bg-gradient-to-r from-orange-600 via-orange-500 to-yellow-500 shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"bg-white p-2 rounded-full\">\n              <Zap className=\"h-6 w-6 text-orange-600\" />\n            </div>\n            <div className=\"text-white\">\n              <h1 className=\"text-xl font-bold\">Chava Electricals</h1>\n              <p className=\"text-xs text-orange-100\">चावा इलेक्ट्रिकल्स</p>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-white hover:text-orange-100 transition-colors duration-200 font-medium group\"\n              >\n                <div className=\"text-center\">\n                  <div className=\"text-sm\">{item.label}</div>\n                  <div className=\"text-xs text-orange-100 group-hover:text-white\">\n                    {item.english}\n                  </div>\n                </div>\n              </Link>\n            ))}\n            \n            {/* Call Button */}\n            <Link\n              href=\"tel:+919876543210\"\n              className=\"bg-white text-orange-600 px-4 py-2 rounded-full font-semibold hover:bg-orange-50 transition-colors duration-200 flex items-center space-x-2\"\n            >\n              <Phone className=\"h-4 w-4\" />\n              <span>कॉल करा</span>\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-white hover:text-orange-100 transition-colors duration-200\"\n            >\n              {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            className=\"md:hidden bg-orange-700\"\n          >\n            <div className=\"px-2 pt-2 pb-3 space-y-1\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-white hover:bg-orange-600 rounded-md transition-colors duration-200\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  <div>{item.label}</div>\n                  <div className=\"text-xs text-orange-200\">{item.english}</div>\n                </Link>\n              ))}\n              <Link\n                href=\"tel:+919876543210\"\n                className=\"block px-3 py-2 bg-white text-orange-600 rounded-md font-semibold hover:bg-orange-50 transition-colors duration-200 flex items-center space-x-2\"\n                onClick={() => setIsOpen(false)}\n              >\n                <Phone className=\"h-4 w-4\" />\n                <span>कॉल करा</span>\n              </Link>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,SAAS;IACb,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;YAAc,SAAS;QAAO;QAClD;YAAE,MAAM;YAAa,OAAO;YAAQ,SAAS;QAAW;QACxD;YAAE,MAAM;YAAY,OAAO;YAAY,SAAS;QAAW;QAC3D;YAAE,MAAM;YAAU,OAAO;YAAiB,SAAS;QAAe;QAClE;YAAE,MAAM;YAAU,OAAO;YAAe,SAAS;QAAQ;QACzD;YAAE,MAAM;YAAY,OAAO;YAAU,SAAS;QAAU;KACzD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoB;;;;;;sDAClC,8OAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;;;;;;;;;sCAK3C,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAW,KAAK,KAAK;;;;;;8DACpC,8OAAC;oDAAI,WAAU;8DACZ,KAAK,OAAO;;;;;;;;;;;;uCAPZ,KAAK,IAAI;;;;;8CAclB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;6FAAe,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9D,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,UAAU;;sDAEzB,8OAAC;sDAAK,KAAK,KAAK;;;;;;sDAChB,8OAAC;4CAAI,WAAU;sDAA2B,KAAK,OAAO;;;;;;;mCANjD,KAAK,IAAI;;;;;0CASlB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;uCAEe", "debugId": null}}]}