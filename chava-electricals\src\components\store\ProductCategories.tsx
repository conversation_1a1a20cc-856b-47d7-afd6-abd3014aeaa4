/*
 * Chava Electricals Website - Product Categories Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { motion } from 'framer-motion';
import { 
  Zap, 
  Lightbulb, 
  Fan, 
  Cable, 
  Settings, 
  Shield,
  Grid3X3,
  Wrench
} from 'lucide-react';

interface ProductCategoriesProps {
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
}

const ProductCategories = ({ selectedCategory, onCategoryChange }: ProductCategoriesProps) => {
  const categories = [
    {
      id: 'all',
      icon: <Grid3X3 className="h-6 w-6" />,
      name: 'सर्व उत्पादने',
      english: 'All Products',
      count: 156,
      color: 'bg-gray-600'
    },
    {
      id: 'switches-sockets',
      icon: <Zap className="h-6 w-6" />,
      name: 'स्विच आणि सॉकेट',
      english: 'Switches & Sockets',
      count: 45,
      color: 'bg-blue-600'
    },
    {
      id: 'lighting',
      icon: <Lightbulb className="h-6 w-6" />,
      name: 'लाइटिंग',
      english: 'Lighting',
      count: 38,
      color: 'bg-yellow-600'
    },
    {
      id: 'fans',
      icon: <Fan className="h-6 w-6" />,
      name: 'फॅन्स',
      english: 'Fans',
      count: 24,
      color: 'bg-green-600'
    },
    {
      id: 'wires-cables',
      icon: <Cable className="h-6 w-6" />,
      name: 'वायर आणि केबल',
      english: 'Wires & Cables',
      count: 32,
      color: 'bg-red-600'
    },
    {
      id: 'accessories',
      icon: <Settings className="h-6 w-6" />,
      name: 'अॅक्सेसरीज',
      english: 'Accessories',
      count: 17,
      color: 'bg-purple-600'
    },
    {
      id: 'safety',
      icon: <Shield className="h-6 w-6" />,
      name: 'सेफ्टी उपकरणे',
      english: 'Safety Equipment',
      count: 12,
      color: 'bg-orange-600'
    },
    {
      id: 'tools',
      icon: <Wrench className="h-6 w-6" />,
      name: 'टूल्स',
      english: 'Tools',
      count: 8,
      color: 'bg-indigo-600'
    }
  ];

  return (
    <div className="mb-12">
      <motion.h3
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-2xl font-bold text-gray-900 mb-6 text-center marathi-text"
      >
        उत्पादन श्रेणी निवडा
      </motion.h3>

      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
        {categories.map((category, index) => (
          <motion.button
            key={category.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            onClick={() => onCategoryChange(category.id)}
            className={`relative p-4 rounded-xl text-center transition-all duration-300 transform hover:scale-105 ${
              selectedCategory === category.id
                ? `${category.color} text-white shadow-lg`
                : 'bg-white text-gray-700 hover:bg-gray-50 shadow-md'
            }`}
          >
            {/* Icon */}
            <div className={`flex justify-center mb-3 ${
              selectedCategory === category.id ? 'text-white' : category.color.replace('bg-', 'text-')
            }`}>
              {category.icon}
            </div>

            {/* Category Name */}
            <div className="space-y-1">
              <div className="text-sm font-semibold marathi-text">
                {category.name}
              </div>
              <div className={`text-xs ${
                selectedCategory === category.id ? 'text-white/80' : 'text-gray-500'
              }`}>
                {category.english}
              </div>
            </div>

            {/* Product Count */}
            <div className={`absolute -top-2 -right-2 text-xs px-2 py-1 rounded-full font-bold ${
              selectedCategory === category.id
                ? 'bg-white/20 text-white'
                : `${category.color} text-white`
            }`}>
              {category.count}
            </div>

            {/* Active Indicator */}
            {selectedCategory === category.id && (
              <motion.div
                layoutId="activeCategory"
                className="absolute inset-0 border-2 border-white rounded-xl"
                initial={false}
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
              />
            )}
          </motion.button>
        ))}
      </div>

      {/* Category Description */}
      <motion.div
        key={selectedCategory}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="mt-6 text-center"
      >
        <div className="inline-flex items-center space-x-2 bg-orange-100 text-orange-800 px-4 py-2 rounded-full">
          <span className="text-sm marathi-text">
            {selectedCategory === 'all' ? 'सर्व उत्पादने दाखवत आहे' : 
             `${categories.find(c => c.id === selectedCategory)?.name} श्रेणी निवडली`}
          </span>
          <span className="text-xs">
            ({categories.find(c => c.id === selectedCategory)?.count} उत्पादने)
          </span>
        </div>
      </motion.div>
    </div>
  );
};

export default ProductCategories;
