/*
 * Chava Electricals Website - Gallery Modal Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, MapPin, Calendar, Play, Camera, Phone, Share2 } from 'lucide-react';
import Image from 'next/image';

interface GalleryModalProps {
  item: any;
  isOpen: boolean;
  onClose: () => void;
}

const GalleryModal = ({ item, isOpen, onClose }: GalleryModalProps) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!item) return null;

  const getCategoryBadge = (category: string) => {
    const badges = {
      'home-wiring': { label: 'घरगुती वायरिंग', color: 'bg-blue-500' },
      'sensor-lighting': { label: 'सेन्सर लाइटिंग', color: 'bg-yellow-500' },
      'repairs': { label: 'रिपेअर सर्विस', color: 'bg-red-500' },
      'fan-installation': { label: 'फॅन इन्स्टॉलेशन', color: 'bg-green-500' },
      'switch-board': { label: 'स्विच बोर्ड', color: 'bg-purple-500' },
    };
    return badges[category] || { label: category, color: 'bg-gray-500' };
  };

  const categoryBadge = getCategoryBadge(item.category);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          {/* Backdrop */}
          <div className="absolute inset-0 bg-black/80 backdrop-blur-sm" />

          {/* Modal Content */}
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="relative bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 z-10 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors duration-200"
            >
              <X className="h-6 w-6" />
            </button>

            <div className="flex flex-col lg:flex-row h-full">
              {/* Media Section */}
              <div className="lg:w-2/3 relative">
                <div className="aspect-[4/3] lg:aspect-auto lg:h-full relative">
                  {item.type === 'video' ? (
                    <div className="w-full h-full bg-black flex items-center justify-center">
                      <div className="text-center text-white">
                        <Play className="h-16 w-16 mx-auto mb-4" />
                        <p className="text-lg marathi-text">व्हिडिओ प्लेयर येथे असेल</p>
                        <p className="text-sm opacity-75">Video player would be here</p>
                      </div>
                    </div>
                  ) : (
                    <Image
                      src={item.image}
                      alt={item.title}
                      fill
                      className="object-cover"
                    />
                  )}
                </div>

                {/* Media Type Badge */}
                <div className="absolute top-4 left-4">
                  <div className={`flex items-center space-x-1 px-3 py-1 rounded-full text-white text-sm font-semibold ${
                    item.type === 'video' ? 'bg-red-500' : 'bg-blue-500'
                  }`}>
                    {item.type === 'video' ? (
                      <Play className="h-4 w-4" />
                    ) : (
                      <Camera className="h-4 w-4" />
                    )}
                    <span>{item.type === 'video' ? 'व्हिडिओ' : 'फोटो'}</span>
                  </div>
                </div>
              </div>

              {/* Content Section */}
              <div className="lg:w-1/3 p-6 lg:p-8 overflow-y-auto">
                {/* Category Badge */}
                <div className="mb-4">
                  <span className={`inline-block px-3 py-1 rounded-full text-white text-sm font-semibold ${categoryBadge.color}`}>
                    {categoryBadge.label}
                  </span>
                </div>

                {/* Title */}
                <h2 className="text-2xl font-bold text-gray-900 mb-2 marathi-text">
                  {item.title}
                </h2>
                <h3 className="text-xl font-semibold text-orange-600 mb-4">
                  {item.englishTitle}
                </h3>

                {/* Description */}
                <div className="mb-6">
                  <p className="text-gray-700 mb-3 marathi-text leading-relaxed">
                    {item.description}
                  </p>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {item.englishDescription}
                  </p>
                </div>

                {/* Meta Information */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center space-x-2 text-gray-600">
                    <MapPin className="h-5 w-5 text-orange-600" />
                    <span className="marathi-text">{item.location}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <Calendar className="h-5 w-5 text-orange-600" />
                    <span>{new Date(item.date).toLocaleDateString('mr-IN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <button className="w-full bg-orange-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-orange-700 transition-colors duration-200 flex items-center justify-center space-x-2">
                    <Phone className="h-5 w-5" />
                    <span className="marathi-text">अशाच कामासाठी कॉल करा</span>
                  </button>
                  
                  <button className="w-full border-2 border-orange-600 text-orange-600 py-3 px-4 rounded-lg font-semibold hover:bg-orange-50 transition-colors duration-200 flex items-center justify-center space-x-2">
                    <Share2 className="h-5 w-5" />
                    <span className="marathi-text">शेअर करा</span>
                  </button>
                </div>

                {/* Additional Info */}
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-2 marathi-text">
                    या प्रकल्पाबद्दल
                  </h4>
                  <ul className="text-sm text-gray-600 space-y-1 marathi-text">
                    <li>• व्यावसायिक इन्स्टॉलेशन</li>
                    <li>• गुणवत्ता हमी</li>
                    <li>• वेळेवर पूर्ण</li>
                    <li>• ग्राहक समाधान</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default GalleryModal;
