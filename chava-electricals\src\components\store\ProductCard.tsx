/*
 * Chava Electricals Website - Product Card Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { motion } from 'framer-motion';
import { Star, ShoppingCart, Heart, Eye } from 'lucide-react';
import Image from 'next/image';

interface ProductCardProps {
  product: {
    id: number;
    name: string;
    englishName: string;
    category: string;
    price: number;
    originalPrice: number;
    image: string;
    brand: string;
    rating: number;
    reviews: number;
    inStock: boolean;
    features: string[];
  };
  onAddToCart: (product: any) => void;
}

const ProductCard = ({ product, onAddToCart }: ProductCardProps) => {
  const discountPercentage = Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);

  return (
    <motion.div
      whileHover={{ y: -8 }}
      className="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden group"
    >
      {/* Image Container */}
      <div className="relative aspect-square overflow-hidden">
        <Image
          src={product.image}
          alt={product.name}
          fill
          className="object-cover transition-transform duration-300 group-hover:scale-110"
        />
        
        {/* Badges */}
        <div className="absolute top-3 left-3 space-y-2">
          {discountPercentage > 0 && (
            <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
              -{discountPercentage}%
            </span>
          )}
          {!product.inStock && (
            <span className="bg-gray-500 text-white px-2 py-1 rounded-full text-xs font-bold marathi-text">
              स्टॉक नाही
            </span>
          )}
        </div>

        {/* Action Buttons */}
        <div className="absolute top-3 right-3 space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button className="bg-white/90 p-2 rounded-full hover:bg-white transition-colors duration-200">
            <Heart className="h-4 w-4 text-gray-600" />
          </button>
          <button className="bg-white/90 p-2 rounded-full hover:bg-white transition-colors duration-200">
            <Eye className="h-4 w-4 text-gray-600" />
          </button>
        </div>

        {/* Quick Add to Cart */}
        <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button
            onClick={() => onAddToCart(product)}
            disabled={!product.inStock}
            className={`w-full py-2 px-4 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center space-x-2 ${
              product.inStock
                ? 'bg-orange-600 text-white hover:bg-orange-700'
                : 'bg-gray-400 text-gray-200 cursor-not-allowed'
            }`}
          >
            <ShoppingCart className="h-4 w-4" />
            <span className="marathi-text">
              {product.inStock ? 'कार्टमध्ये टाका' : 'स्टॉक नाही'}
            </span>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Brand */}
        <div className="text-sm text-orange-600 font-semibold mb-1">
          {product.brand}
        </div>

        {/* Title */}
        <h3 className="text-lg font-bold text-gray-900 mb-1 marathi-text line-clamp-1">
          {product.name}
        </h3>
        <h4 className="text-sm font-medium text-gray-600 mb-3 line-clamp-1">
          {product.englishName}
        </h4>

        {/* Rating */}
        <div className="flex items-center space-x-2 mb-3">
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-4 w-4 ${
                  i < Math.floor(product.rating)
                    ? 'text-yellow-400 fill-current'
                    : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <span className="text-sm text-gray-600">
            {product.rating} ({product.reviews})
          </span>
        </div>

        {/* Features */}
        <div className="mb-4">
          <ul className="space-y-1">
            {product.features.slice(0, 2).map((feature, index) => (
              <li key={index} className="flex items-center text-xs text-gray-600">
                <div className="w-1.5 h-1.5 bg-orange-600 rounded-full mr-2"></div>
                <span className="marathi-text">{feature}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Price */}
        <div className="flex items-center justify-between mb-4">
          <div className="space-y-1">
            <div className="text-xl font-bold text-gray-900">
              ₹{product.price}
            </div>
            {product.originalPrice > product.price && (
              <div className="text-sm text-gray-500 line-through">
                ₹{product.originalPrice}
              </div>
            )}
          </div>
          <div className="text-right">
            <div className="text-xs text-gray-500 marathi-text">प्रति पीस</div>
            <div className="text-xs text-gray-400">per piece</div>
          </div>
        </div>

        {/* Add to Cart Button */}
        <button
          onClick={() => onAddToCart(product)}
          disabled={!product.inStock}
          className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2 ${
            product.inStock
              ? 'bg-orange-600 text-white hover:bg-orange-700 transform hover:scale-105'
              : 'bg-gray-400 text-gray-200 cursor-not-allowed'
          }`}
        >
          <ShoppingCart className="h-5 w-5" />
          <span className="marathi-text">
            {product.inStock ? 'कार्टमध्ये टाका' : 'स्टॉक नाही'}
          </span>
        </button>
      </div>
    </motion.div>
  );
};

export default ProductCard;
